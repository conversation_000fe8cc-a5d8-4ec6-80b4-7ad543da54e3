from flask import Flask, request, jsonify, Response
from flask_cors import CORS
import json
import requests
import time
import os
from cryptography.fernet import Fe<PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

app = Flask(__name__)
CORS(app)

# Load configuration
def load_config():
    config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'providers.json')
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return {"providers": []}

@app.route('/api/config', methods=['GET'])
def get_config():
    """Get available providers and models"""
    config = load_config()
    return jsonify(config)

@app.route('/api/generate', methods=['POST'])
def generate_text():
    """Generate text using specified provider and model"""
    data = request.json
    provider = data.get('provider')
    model = data.get('model')
    prompt = data.get('prompt')
    system_prompt = data.get('system_prompt', '')
    temperature = data.get('temperature', 0.7)
    max_tokens = data.get('max_tokens', 150)
    api_key = data.get('api_key', '')
    
    if not all([provider, model, prompt]):
        return jsonify({'error': 'Missing required parameters'}), 400
    
    try:
        # Route to appropriate provider
        if provider == 'google':
            result = generate_google(model, prompt, system_prompt, temperature, max_tokens, api_key)
        elif provider == 'together':
            result = generate_together(model, prompt, system_prompt, temperature, max_tokens, api_key)
        elif provider == 'openrouter':
            result = generate_openrouter(model, prompt, system_prompt, temperature, max_tokens, api_key)
        elif provider == 'cohere':
            result = generate_cohere(model, prompt, system_prompt, temperature, max_tokens, api_key)
        elif provider == 'mistral':
            result = generate_mistral(model, prompt, system_prompt, temperature, max_tokens, api_key)
        elif provider == 'groq':
            result = generate_groq(model, prompt, system_prompt, temperature, max_tokens, api_key)
        elif provider == 'deepseek':
            result = generate_deepseek(model, prompt, system_prompt, temperature, max_tokens, api_key)
        elif provider == 'huggingface':
            result = generate_huggingface(model, prompt, system_prompt, temperature, max_tokens, api_key)
        else:
            return jsonify({'error': 'Unsupported provider'}), 400
            
        return jsonify({'text': result})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/generate-stream', methods=['POST'])
def generate_text_stream():
    """Generate text with streaming response"""
    data = request.json
    provider = data.get('provider')
    model = data.get('model')
    prompt = data.get('prompt')
    system_prompt = data.get('system_prompt', '')
    temperature = data.get('temperature', 0.7)
    max_tokens = data.get('max_tokens', 150)
    api_key = data.get('api_key', '')
    
    def generate():
        try:
            # For now, simulate streaming by chunking the response
            # In production, you'd use actual streaming APIs
            if provider == 'google':
                result = generate_google(model, prompt, system_prompt, temperature, max_tokens, api_key)
            elif provider == 'together':
                result = generate_together(model, prompt, system_prompt, temperature, max_tokens, api_key)
            elif provider == 'openrouter':
                result = generate_openrouter(model, prompt, system_prompt, temperature, max_tokens, api_key)
            elif provider == 'cohere':
                result = generate_cohere(model, prompt, system_prompt, temperature, max_tokens, api_key)
            elif provider == 'mistral':
                result = generate_mistral(model, prompt, system_prompt, temperature, max_tokens, api_key)
            elif provider == 'groq':
                result = generate_groq(model, prompt, system_prompt, temperature, max_tokens, api_key)
            elif provider == 'deepseek':
                result = generate_deepseek(model, prompt, system_prompt, temperature, max_tokens, api_key)
            elif provider == 'huggingface':
                result = generate_huggingface(model, prompt, system_prompt, temperature, max_tokens, api_key)
            else:
                yield f"data: {json.dumps({'error': 'Unsupported provider'})}\n\n"
                return
            
            # Simulate streaming by sending chunks
            words = result.split()
            for i, word in enumerate(words):
                chunk = word + (' ' if i < len(words) - 1 else '')
                yield f"data: {json.dumps({'chunk': chunk})}\n\n"
                time.sleep(0.05)  # Small delay for streaming effect
            
            yield f"data: {json.dumps({'done': True})}\n\n"
            
        except Exception as e:
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
    
    return Response(generate(), mimetype='text/plain')

# Provider-specific generation functions
def generate_google(model, prompt, system_prompt, temperature, max_tokens, api_key):
    """Generate text using Google Gemini API"""
    url = f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={api_key}"
    
    messages = []
    if system_prompt:
        messages.append({"role": "user", "parts": [{"text": system_prompt}]})
    messages.append({"role": "user", "parts": [{"text": prompt}]})
    
    payload = {
        "contents": messages,
        "generationConfig": {
            "temperature": temperature,
            "maxOutputTokens": max_tokens
        }
    }
    
    response = requests.post(url, json=payload)
    response.raise_for_status()
    
    result = response.json()
    return result['candidates'][0]['content']['parts'][0]['text']

def generate_together(model, prompt, system_prompt, temperature, max_tokens, api_key):
    """Generate text using Together AI API"""
    url = "https://api.together.xyz/v1/chat/completions"
    
    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": prompt})
    
    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens
    }
    
    headers = {"Authorization": f"Bearer {api_key}"}
    response = requests.post(url, json=payload, headers=headers)
    response.raise_for_status()
    
    result = response.json()
    return result['choices'][0]['message']['content']

def generate_openrouter(model, prompt, system_prompt, temperature, max_tokens, api_key):
    """Generate text using OpenRouter API"""
    url = "https://openrouter.ai/api/v1/chat/completions"
    
    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": prompt})
    
    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens
    }
    
    headers = {"Authorization": f"Bearer {api_key}"}
    response = requests.post(url, json=payload, headers=headers)
    response.raise_for_status()
    
    result = response.json()
    return result['choices'][0]['message']['content']

def generate_cohere(model, prompt, system_prompt, temperature, max_tokens, api_key):
    """Generate text using Cohere API"""
    url = "https://api.cohere.ai/v1/generate"
    
    full_prompt = f"{system_prompt}\n\n{prompt}" if system_prompt else prompt
    
    payload = {
        "model": model,
        "prompt": full_prompt,
        "temperature": temperature,
        "max_tokens": max_tokens
    }
    
    headers = {"Authorization": f"Bearer {api_key}"}
    response = requests.post(url, json=payload, headers=headers)
    response.raise_for_status()
    
    result = response.json()
    return result['generations'][0]['text']

def generate_mistral(model, prompt, system_prompt, temperature, max_tokens, api_key):
    """Generate text using Mistral API"""
    url = "https://api.mistral.ai/v1/chat/completions"
    
    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": prompt})
    
    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens
    }
    
    headers = {"Authorization": f"Bearer {api_key}"}
    response = requests.post(url, json=payload, headers=headers)
    response.raise_for_status()
    
    result = response.json()
    return result['choices'][0]['message']['content']

def generate_groq(model, prompt, system_prompt, temperature, max_tokens, api_key):
    """Generate text using Groq API"""
    url = "https://api.groq.com/openai/v1/chat/completions"
    
    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": prompt})
    
    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens
    }
    
    headers = {"Authorization": f"Bearer {api_key}"}
    response = requests.post(url, json=payload, headers=headers)
    response.raise_for_status()
    
    result = response.json()
    return result['choices'][0]['message']['content']

def generate_deepseek(model, prompt, system_prompt, temperature, max_tokens, api_key):
    """Generate text using DeepSeek API"""
    url = "https://api.deepseek.com/v1/chat/completions"
    
    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": prompt})
    
    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens
    }
    
    headers = {"Authorization": f"Bearer {api_key}"}
    response = requests.post(url, json=payload, headers=headers)
    response.raise_for_status()
    
    result = response.json()
    return result['choices'][0]['message']['content']

def generate_huggingface(model, prompt, system_prompt, temperature, max_tokens, api_key):
    """Generate text using Hugging Face API"""
    url = f"https://api-inference.huggingface.co/models/{model}"
    
    full_prompt = f"{system_prompt}\n\n{prompt}" if system_prompt else prompt
    
    payload = {
        "inputs": full_prompt,
        "parameters": {
            "temperature": temperature,
            "max_new_tokens": max_tokens
        }
    }
    
    headers = {"Authorization": f"Bearer {api_key}"}
    response = requests.post(url, json=payload, headers=headers)
    response.raise_for_status()
    
    result = response.json()
    return result[0]['generated_text'][len(full_prompt):].strip()

# Encryption utilities
def derive_key(password: str, salt: bytes) -> bytes:
    """Derive encryption key from password"""
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    return base64.urlsafe_b64encode(kdf.derive(password.encode()))

@app.route('/api/encrypt', methods=['POST'])
def encrypt_data():
    """Encrypt story data with user password"""
    try:
        data = request.json
        password = data.get('password')
        story_data = data.get('data')
        
        if not password or not story_data:
            return jsonify({'error': 'Password and data required'}), 400
        
        # Generate salt
        salt = os.urandom(16)
        key = derive_key(password, salt)
        
        # Encrypt data
        f = Fernet(key)
        encrypted_data = f.encrypt(json.dumps(story_data).encode())
        
        # Combine salt and encrypted data
        result = base64.urlsafe_b64encode(salt + encrypted_data).decode()
        
        return jsonify({'encrypted': result})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/decrypt', methods=['POST'])
def decrypt_data():
    """Decrypt story data with user password"""
    try:
        data = request.json
        password = data.get('password')
        encrypted_data = data.get('encrypted')
        
        if not password or not encrypted_data:
            return jsonify({'error': 'Password and encrypted data required'}), 400
        
        # Decode and separate salt from encrypted data
        combined = base64.urlsafe_b64decode(encrypted_data.encode())
        salt = combined[:16]
        encrypted = combined[16:]
        
        # Derive key and decrypt
        key = derive_key(password, salt)
        f = Fernet(key)
        decrypted_data = f.decrypt(encrypted)
        
        # Parse JSON
        result = json.loads(decrypted_data.decode())
        
        return jsonify({'data': result})
    except Exception as e:
        return jsonify({'error': 'Invalid password or corrupted data'}), 400

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
