# AI Story Writer

A powerful, privacy-focused AI story writing and roleplaying application with support for multiple AI providers and automatic image generation.

## Features

### ✨ Core Features
- **Multi-Provider AI Support**: Google Gemini, Together AI, OpenRouter, Cohere, Mistral, Groq, DeepSeek, and Hugging Face
- **Real-time Text Generation**: AI continues writing from exactly where you stop
- **Streaming Responses**: See text generate in real-time for better UX
- **Smart Accept/Reject**: Generated text appears in grey with easy accept/reject controls
- **Auto-scroll**: Textbox automatically scrolls and takes most of the page

### 🛠️ Quality of Life Features
- **Undo/Redo**: Full history with keyboard shortcuts (Ctrl+Z, Ctrl+Y)
- **Auto-save**: Automatically saves your work to local storage
- **Word/Character Count**: Real-time statistics in the header
- **Keyboard Shortcuts**: Tab to accept, Ctrl+Enter to generate
- **Loading Indicators**: Clear feedback when AI is generating

### 🔒 Privacy & Security
- **Encrypted Export/Import**: Stories encrypted with user-defined passwords
- **Local Storage**: All settings and stories stored locally
- **No Data Collection**: Your stories never leave your device (except API calls)

### ⚙️ Configuration
- **Collapsible Sidebar**: Clean interface with easy access to settings
- **Provider Selection**: Choose from 8+ AI providers
- **Model Selection**: Pick the best model for your needs
- **Advanced Settings**: Temperature, max tokens, system prompts
- **Persistent Settings**: All preferences saved automatically

## Quick Start

### One-Click Setup (Windows)
1. Download or clone this repository
2. Double-click `run.bat`
3. The installer will:
   - Check for Python and Node.js
   - Install dependencies automatically
   - Start both backend and frontend
   - Open your browser to the app

### Manual Setup

#### Prerequisites
- Python 3.8+ ([Download](https://python.org))
- Node.js 16+ ([Download](https://nodejs.org))

#### Installation
```bash
# Clone the repository
git clone <repository-url>
cd ai-story-writer

# Install Python dependencies
python -m venv venv
venv\Scripts\activate  # On Windows
# source venv/bin/activate  # On macOS/Linux
pip install -r requirements.txt

# Install Node.js dependencies
cd frontend
npm install
cd ..

# Start the application
# Terminal 1: Start backend
venv\Scripts\activate
python backend/app.py

# Terminal 2: Start frontend
cd frontend
npm run dev
```

## Configuration

### Adding API Keys
1. Open the app and click the Settings button
2. Select your preferred AI provider
3. Click "Get API Key" to get your free API key
4. Paste the key in the API Key field
5. Select a model and start writing!

### Supported Providers

| Provider | Free Tier | Models Available | Get API Key |
|----------|-----------|------------------|-------------|
| **Google AI** | ✅ Generous | Gemini 1.5 Flash, Pro | [Get Key](https://aistudio.google.com/app/apikey) |
| **Together AI** | ✅ 3 months free | Llama 3.3 70B, Mistral, Qwen | [Get Key](https://api.together.xyz/settings/api-keys) |
| **OpenRouter** | ✅ Free models | Gemini 2.0, Llama 3.1, Mistral | [Get Key](https://openrouter.ai/keys) |
| **Groq** | ✅ Fast inference | Llama 3.1 70B, Mixtral, Gemma | [Get Key](https://console.groq.com/keys) |
| **DeepSeek** | ✅ Competitive | DeepSeek Chat, Reasoner | [Get Key](https://platform.deepseek.com/api_keys) |
| **Cohere** | ✅ Limited free | Command R, Command R+ | [Get Key](https://dashboard.cohere.com/api-keys) |
| **Mistral** | ✅ Limited free | Mistral Small, Codestral | [Get Key](https://console.mistral.ai/api-keys/) |
| **Hugging Face** | ✅ Free tier | Llama 2, Mistral, CodeLlama | [Get Key](https://huggingface.co/settings/tokens) |

### Customizing Providers
Edit `config/providers.json` to add new providers or models:

```json
{
  "providers": [
    {
      "id": "custom-provider",
      "name": "Custom Provider",
      "models": [
        {
          "id": "custom-model",
          "name": "Custom Model",
          "description": "Your custom model description"
        }
      ],
      "api_key_required": true,
      "api_key_url": "https://your-provider.com/api-keys"
    }
  ]
}
```

## Usage

### Basic Writing
1. Start typing your story in the main text area
2. When you pause, the AI will automatically continue from your last word
3. Generated text appears in grey
4. Press **Tab** or click the ✓ button to accept
5. Click elsewhere or press **Escape** to reject

### Keyboard Shortcuts
- **Tab**: Accept generated text
- **Ctrl+Enter**: Generate text manually
- **Ctrl+Z**: Undo
- **Ctrl+Y**: Redo

### Export/Import Stories
1. Click **Export Story** in the sidebar
2. Enter a password to encrypt your story
3. Save the `.aistory` file
4. To import: Click **Import Story**, select file, enter password

## Technical Details

### Architecture
- **Frontend**: Vue.js 3 with Vite
- **Backend**: Python Flask with CORS
- **Storage**: Browser localStorage + encrypted file export
- **Encryption**: AES-256 with PBKDF2 key derivation

### File Structure
```
ai-story-writer/
├── run.bat                 # One-click installer/runner
├── requirements.txt        # Python dependencies
├── backend/
│   └── app.py             # Flask API server
├── frontend/
│   ├── package.json       # Node.js dependencies
│   ├── vite.config.js     # Vite configuration
│   ├── index.html         # Main HTML file
│   └── src/
│       ├── App.vue        # Main Vue component
│       ├── components/    # Vue components
│       └── composables/   # Reusable logic
└── config/
    └── providers.json     # AI provider configuration
```

### API Endpoints
- `GET /api/config` - Get available providers and models
- `POST /api/generate` - Generate text (single response)
- `POST /api/generate-stream` - Generate text (streaming)
- `POST /api/encrypt` - Encrypt story data
- `POST /api/decrypt` - Decrypt story data

## Troubleshooting

### Common Issues

**"Python is not installed"**
- Download Python from [python.org](https://python.org)
- Make sure to check "Add Python to PATH" during installation

**"Node.js is not installed"**
- Download Node.js from [nodejs.org](https://nodejs.org)
- Restart your terminal after installation

**"API Key Invalid"**
- Double-check your API key is correct
- Make sure you're using the right provider
- Some providers require billing setup even for free tiers

**"Generation Failed"**
- Check your internet connection
- Verify your API key is valid and has quota remaining
- Try a different model or provider

### Getting Help
1. Check the browser console for error messages (F12)
2. Look at the terminal output for backend errors
3. Verify your API keys are working with the provider's documentation

## Roadmap

### Coming Soon
- **Image Generation**: Automatic scene illustration
- **Character System**: Persistent character profiles
- **Roleplay Mode**: Multi-character conversations
- **Story Templates**: Pre-built story structures
- **Export Formats**: PDF, EPUB, Word document export

### Future Features
- **Collaborative Writing**: Real-time collaboration
- **Voice Input**: Dictate your stories
- **Mobile App**: Native mobile applications
- **Cloud Sync**: Optional cloud backup

## Contributing

This is a personal project, but suggestions and bug reports are welcome!

## License

MIT License - feel free to modify and distribute as needed.
