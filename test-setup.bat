@echo off
title AI Story Writer - Setup Test
echo ========================================
echo    AI Story Writer - Setup Test
echo ========================================
echo.

echo [TEST] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [FAIL] Python is not installed or not in PATH
    echo Please install Python from: https://python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    goto :test_node
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo [PASS] Python %PYTHON_VERSION% found
)

echo [TEST] Checking pip...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [FAIL] pip is not available
) else (
    echo [PASS] pip is available
)

echo [TEST] Testing virtual environment creation...
if exist "test-venv" rmdir /s /q test-venv
python -m venv test-venv >nul 2>&1
if %errorlevel% neq 0 (
    echo [FAIL] Cannot create virtual environment
) else (
    echo [PASS] Virtual environment creation works
    rmdir /s /q test-venv
)

:test_node
echo.
echo [TEST] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [FAIL] Node.js is not installed or not in PATH
    echo Please install Node.js from: https://nodejs.org/
    goto :test_files
) else (
    for /f "tokens=1" %%i in ('node --version 2^>^&1') do set NODE_VERSION=%%i
    echo [PASS] Node.js %NODE_VERSION% found
)

echo [TEST] Checking npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [FAIL] npm is not available
) else (
    for /f "tokens=1" %%i in ('npm --version 2^>^&1') do set NPM_VERSION=%%i
    echo [PASS] npm %NPM_VERSION% found
)

:test_files
echo.
echo [TEST] Checking required files...

if not exist "requirements.txt" (
    echo [FAIL] requirements.txt not found
) else (
    echo [PASS] requirements.txt found
)

if not exist "backend\app.py" (
    echo [FAIL] backend\app.py not found
) else (
    echo [PASS] backend\app.py found
)

if not exist "frontend\package.json" (
    echo [FAIL] frontend\package.json not found
) else (
    echo [PASS] frontend\package.json found
)

if not exist "config\providers.json" (
    echo [FAIL] config\providers.json not found
) else (
    echo [PASS] config\providers.json found
)

echo.
echo [TEST] Testing Python requirements...
python -c "import sys; print('Python executable:', sys.executable)" 2>nul
if %errorlevel% neq 0 (
    echo [FAIL] Python execution test failed
) else (
    echo [PASS] Python execution works
)

echo.
echo ========================================
echo           Test Results Summary
echo ========================================

:: Check if all critical components are available
python --version >nul 2>&1
set PYTHON_OK=%errorlevel%

node --version >nul 2>&1
set NODE_OK=%errorlevel%

if %PYTHON_OK% equ 0 if %NODE_OK% equ 0 (
    echo [SUCCESS] All dependencies are installed!
    echo You can now run run.bat to start the application.
) else (
    echo [WARNING] Some dependencies are missing:
    if %PYTHON_OK% neq 0 echo - Python is not installed
    if %NODE_OK% neq 0 echo - Node.js is not installed
    echo.
    echo Run install-dependencies.bat to download the installers.
)

echo.
pause
