{"providers": [{"id": "google", "name": "Google AI (Gemini)", "models": [{"id": "gemini-1.5-flash", "name": "Gemini 1.5 Flash", "description": "Fast and efficient model for creative writing"}, {"id": "gemini-1.5-pro", "name": "Gemini 1.5 Pro", "description": "Advanced model with superior reasoning"}], "api_key_required": true, "api_key_url": "https://aistudio.google.com/app/apikey"}, {"id": "together", "name": "Together AI", "models": [{"id": "meta-llama/Llama-3.3-70B-Instruct-Turbo", "name": "Llama 3.3 70B", "description": "Large language model excellent for storytelling"}, {"id": "mistralai/Mistral-7B-Instruct-v0.3", "name": "Mistral 7B", "description": "Efficient model for creative writing"}, {"id": "Qwen/Qwen2.5-7B-Instruct-Turbo", "name": "<PERSON><PERSON> 2.5 7B", "description": "Multilingual model with strong creative abilities"}, {"id": "deepseek-ai/DeepSeek-R1-Distill-Llama-70B", "name": "DeepSeek R1 70B", "description": "Advanced reasoning model for complex narratives"}], "api_key_required": true, "api_key_url": "https://api.together.xyz/settings/api-keys"}, {"id": "openrouter", "name": "OpenRouter", "models": [{"id": "google/gemini-2.0-flash-exp:free", "name": "Gemini 2.0 Flash (Free)", "description": "Free Google model via OpenRouter"}, {"id": "meta-llama/llama-3.1-8b-instruct:free", "name": "Llama 3.1 8B (Free)", "description": "Free Llama model via OpenRouter"}, {"id": "mistralai/mistral-7b-instruct:free", "name": "Mistral 7B (Free)", "description": "Free Mistral model via OpenRouter"}], "api_key_required": true, "api_key_url": "https://openrouter.ai/keys"}, {"id": "cohere", "name": "Cohere", "models": [{"id": "command-r", "name": "Command R", "description": "Structured text generation model"}, {"id": "command-r-plus", "name": "Command R+", "description": "Advanced model with enhanced capabilities"}], "api_key_required": true, "api_key_url": "https://dashboard.cohere.com/api-keys"}, {"id": "mistral", "name": "Mistral AI", "models": [{"id": "mistral-small-latest", "name": "Mistra<PERSON> Small", "description": "Efficient model for creative tasks"}, {"id": "codestral-latest", "name": "Codestral", "description": "Code-focused model, good for structured writing"}], "api_key_required": true, "api_key_url": "https://console.mistral.ai/api-keys/"}, {"id": "groq", "name": "Groq", "models": [{"id": "llama-3.1-70b-versatile", "name": "Llama 3.1 70B", "description": "Fast inference with high quality output"}, {"id": "mixtral-8x7b-32768", "name": "Mixtral 8x7B", "description": "Mixture of experts model for diverse writing"}, {"id": "gemma-7b-it", "name": "Gemma 7B", "description": "Google's open model with good performance"}], "api_key_required": true, "api_key_url": "https://console.groq.com/keys"}, {"id": "deepseek", "name": "DeepSeek", "models": [{"id": "deepseek-chat", "name": "DeepSeek Chat", "description": "Advanced reasoning model matching GPT-4 quality"}, {"id": "deepseek-reasoner", "name": "DeepSeek Reasoner", "description": "Specialized reasoning model for complex narratives"}], "api_key_required": true, "api_key_url": "https://platform.deepseek.com/api_keys"}, {"id": "huggingface", "name": "Hugging Face", "models": [{"id": "meta-llama/Llama-2-7b-chat-hf", "name": "Llama 2 7B Chat", "description": "Open source conversational model"}, {"id": "mistralai/Mistral-7B-Instruct-v0.1", "name": "Mistral 7B Instruct", "description": "Instruction-tuned model for creative tasks"}, {"id": "codellama/CodeLlama-7b-Instruct-hf", "name": "CodeLlama 7B", "description": "Code-focused model for structured writing"}], "api_key_required": true, "api_key_url": "https://huggingface.co/settings/tokens"}]}