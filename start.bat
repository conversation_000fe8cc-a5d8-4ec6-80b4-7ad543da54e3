@echo off
setlocal enabledelayedexpansion
title AI Story Writer
cls

echo ==========================================
echo        AI Story Writer - Auto Setup
echo ==========================================
echo.

:: Check if we're in the right directory
if not exist "backend\app.py" (
    echo [ERROR] Please run this script from the project root directory
    echo Looking for: backend\app.py
    pause
    exit /b 1
)

:: Try to find Python executable
set PYTHON_CMD=
for %%i in (python python3 py) do (
    %%i --version >nul 2>&1
    if !errorlevel! equ 0 (
        set PYTHON_CMD=%%i
        goto :found_python
    )
)

echo [ERROR] Python not found in PATH
echo.
echo Please install Python from: https://python.org/downloads/
echo Make sure to check "Add Python to PATH" during installation
echo.
pause
exit /b 1

:found_python
echo [OK] Found Python: %PYTHON_CMD%

:: Try to find Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js not found in PATH
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo.
    pause
    exit /b 1
)
echo [OK] Found Node.js

echo.
echo [1/4] Setting up Python environment...

:: Create virtual environment
if exist "venv" (
    echo Virtual environment already exists
) else (
    echo Creating virtual environment...
    %PYTHON_CMD% -m venv venv
    if !errorlevel! neq 0 (
        echo [ERROR] Failed to create virtual environment
        echo Make sure you have the 'venv' module installed
        pause
        exit /b 1
    )
)

:: Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo [ERROR] Failed to activate virtual environment
    pause
    exit /b 1
)

echo [2/4] Installing Python dependencies...
pip install --upgrade pip >nul 2>&1
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install Python dependencies
    echo Check your internet connection and try again
    pause
    exit /b 1
)

echo [3/4] Installing Node.js dependencies...
cd frontend
if exist "node_modules" (
    echo Node modules already exist
) else (
    echo Installing Node.js packages...
    npm install
    if !errorlevel! neq 0 (
        echo [ERROR] Failed to install Node.js dependencies
        echo Check your internet connection and try again
        cd ..
        pause
        exit /b 1
    )
)
cd ..

echo [4/4] Starting servers...
echo.
echo Backend: http://localhost:5000
echo Frontend: http://localhost:3000
echo.
echo Starting backend server...
start /b cmd /c "call venv\Scripts\activate.bat && python backend\app.py"

echo Starting frontend server...
cd frontend
start /b cmd /c "npm run dev"
cd ..

echo Waiting for servers to start...
timeout /t 8 /nobreak >nul

echo Opening browser...
start http://localhost:3000

echo.
echo ==========================================
echo     AI Story Writer is now running!
echo ==========================================
echo.
echo App URL: http://localhost:3000
echo.
echo Press any key to stop the servers...
pause >nul

echo.
echo Stopping servers...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1
echo Done.
pause
