@echo off
title AI Story Writer - Launcher
echo ========================================
echo    AI Story Writer - Smart Launcher
echo ========================================
echo.

:: Check if PowerShell is available (Windows 7+ has it)
powershell -Command "Get-Host" >nul 2>&1
if %errorlevel% equ 0 (
    echo [INFO] Using PowerShell launcher for better compatibility...
    powershell -ExecutionPolicy Bypass -File "run.ps1"
) else (
    echo [INFO] Using batch launcher...
    call run.bat
)

pause
