<template>
  <div class="app">
    <!-- Sidebar -->
    <Sidebar 
      :is-open="sidebarOpen"
      :config="config"
      :settings="settings"
      @toggle="toggleSidebar"
      @update-settings="updateSettings"
      @export="exportStory"
      @import="importStory"
    />
    
    <!-- Main Content -->
    <div class="main-content" :class="{ 'sidebar-open': sidebarOpen }">
      <!-- Header -->
      <div class="header">
        <button class="btn btn-secondary" @click="toggleSidebar">
          <span v-if="sidebarOpen">←</span>
          <span v-else">☰</span>
          {{ sidebarOpen ? 'Hide' : 'Settings' }}
        </button>
        
        <div class="header-info">
          <span class="word-count">{{ wordCount }} words</span>
          <span class="char-count">{{ charCount }} characters</span>
        </div>
      </div>
      
      <!-- Story Editor -->
      <StoryEditor
        v-model="storyText"
        :is-generating="isGenerating"
        :generated-text="generatedText"
        :error="error"
        @generate="generateText"
        @accept-generated="acceptGenerated"
        @reject-generated="rejectGenerated"
        @stop-generation="stopGeneration"
      />
    </div>
    
    <!-- Import Modal -->
    <ImportModal
      v-if="showImportModal"
      @close="showImportModal = false"
      @import="handleImport"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import Sidebar from './components/Sidebar.vue'
import StoryEditor from './components/StoryEditor.vue'
import ImportModal from './components/ImportModal.vue'
import { useStorage } from './composables/useStorage'
import { useApi } from './composables/useApi'

export default {
  name: 'App',
  components: {
    Sidebar,
    StoryEditor,
    ImportModal
  },
  setup() {
    // Reactive state
    const sidebarOpen = ref(true)
    const storyText = ref('')
    const generatedText = ref('')
    const isGenerating = ref(false)
    const error = ref('')
    const config = ref({ providers: [] })
    const showImportModal = ref(false)
    
    // Default settings
    const defaultSettings = {
      provider: '',
      model: '',
      apiKey: '',
      systemPrompt: 'You are a creative writing assistant. Continue the story naturally and engagingly.',
      temperature: 0.7,
      maxTokens: 150,
      autoSave: true
    }
    
    const settings = ref({ ...defaultSettings })
    
    // Composables
    const { saveToStorage, loadFromStorage } = useStorage()
    const { generateText: apiGenerateText, loadConfig } = useApi()
    
    // Computed properties
    const wordCount = computed(() => {
      return storyText.value.trim().split(/\s+/).filter(word => word.length > 0).length
    })
    
    const charCount = computed(() => {
      return storyText.value.length
    })
    
    // Methods
    const toggleSidebar = () => {
      sidebarOpen.value = !sidebarOpen.value
      saveToStorage('sidebarOpen', sidebarOpen.value)
    }
    
    const updateSettings = (newSettings) => {
      settings.value = { ...settings.value, ...newSettings }
      saveToStorage('settings', settings.value)
    }
    
    const generateText = async () => {
      if (isGenerating.value || !storyText.value.trim()) return
      
      error.value = ''
      isGenerating.value = true
      generatedText.value = ''
      
      try {
        const response = await apiGenerateText({
          provider: settings.value.provider,
          model: settings.value.model,
          prompt: storyText.value,
          system_prompt: settings.value.systemPrompt,
          temperature: settings.value.temperature,
          max_tokens: settings.value.maxTokens,
          api_key: settings.value.apiKey
        })
        
        generatedText.value = response
      } catch (err) {
        error.value = err.message || 'Failed to generate text'
      } finally {
        isGenerating.value = false
      }
    }
    
    const acceptGenerated = () => {
      if (generatedText.value) {
        storyText.value += generatedText.value
        generatedText.value = ''
        if (settings.value.autoSave) {
          saveToStorage('storyText', storyText.value)
        }
      }
    }
    
    const rejectGenerated = () => {
      generatedText.value = ''
    }
    
    const stopGeneration = () => {
      isGenerating.value = false
      // In a real implementation, you'd cancel the API request here
    }
    
    const exportStory = async (password) => {
      try {
        const data = {
          story: storyText.value,
          settings: settings.value,
          timestamp: new Date().toISOString()
        }
        
        const response = await fetch('/api/encrypt', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ password, data })
        })
        
        const result = await response.json()
        if (result.error) throw new Error(result.error)
        
        // Download file
        const blob = new Blob([result.encrypted], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `story-${new Date().toISOString().split('T')[0]}.aistory`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        
        return { success: true }
      } catch (err) {
        return { success: false, error: err.message }
      }
    }
    
    const importStory = () => {
      showImportModal.value = true
    }
    
    const handleImport = async (file, password) => {
      try {
        const text = await file.text()
        
        const response = await fetch('/api/decrypt', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ password, encrypted: text })
        })
        
        const result = await response.json()
        if (result.error) throw new Error(result.error)
        
        // Load imported data
        if (result.data.story) {
          storyText.value = result.data.story
        }
        if (result.data.settings) {
          settings.value = { ...defaultSettings, ...result.data.settings }
        }
        
        // Save to storage
        saveToStorage('storyText', storyText.value)
        saveToStorage('settings', settings.value)
        
        showImportModal.value = false
        return { success: true }
      } catch (err) {
        return { success: false, error: err.message }
      }
    }
    
    // Auto-save story text
    watch(storyText, (newText) => {
      if (settings.value.autoSave) {
        saveToStorage('storyText', newText)
      }
    }, { debounce: 1000 })
    
    // Load initial data
    onMounted(async () => {
      // Load config
      config.value = await loadConfig()
      
      // Load saved data
      const savedSidebarOpen = loadFromStorage('sidebarOpen')
      if (savedSidebarOpen !== null) {
        sidebarOpen.value = savedSidebarOpen
      }
      
      const savedSettings = loadFromStorage('settings')
      if (savedSettings) {
        settings.value = { ...defaultSettings, ...savedSettings }
      }
      
      const savedStoryText = loadFromStorage('storyText')
      if (savedStoryText) {
        storyText.value = savedStoryText
      }
      
      // Set default provider/model if none selected
      if (!settings.value.provider && config.value.providers.length > 0) {
        const firstProvider = config.value.providers[0]
        settings.value.provider = firstProvider.id
        if (firstProvider.models.length > 0) {
          settings.value.model = firstProvider.models[0].id
        }
      }
    })
    
    return {
      sidebarOpen,
      storyText,
      generatedText,
      isGenerating,
      error,
      config,
      settings,
      showImportModal,
      wordCount,
      charCount,
      toggleSidebar,
      updateSettings,
      generateText,
      acceptGenerated,
      rejectGenerated,
      stopGeneration,
      exportStory,
      importStory,
      handleImport
    }
  }
}
</script>

<style scoped>
.app {
  height: 100vh;
  display: flex;
  background: var(--bg-primary);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.3s ease;
}

.main-content.sidebar-open {
  margin-left: 0;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border);
  flex-shrink: 0;
}

.header-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: var(--text-secondary);
}

@media (max-width: 768px) {
  .main-content.sidebar-open {
    margin-left: 0;
  }
  
  .header-info {
    display: none;
  }
}
</style>
