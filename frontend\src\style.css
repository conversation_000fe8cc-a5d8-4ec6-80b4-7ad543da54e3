/* Global Styles */
:root {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3d3d3d;
  --text-primary: #e0e0e0;
  --text-secondary: #b0b0b0;
  --text-generated: #888888;
  --accent: #4a9eff;
  --accent-hover: #3a8eef;
  --success: #4caf50;
  --error: #f44336;
  --warning: #ff9800;
  --border: #404040;
  --shadow: rgba(0, 0, 0, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border);
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: var(--accent);
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn:hover {
  background: var(--accent-hover);
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-success {
  background: var(--success);
}

.btn-success:hover {
  background: #45a049;
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background: var(--border);
}

/* Input Styles */
.input, .select, .textarea {
  padding: 8px 12px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.input:focus, .select:focus, .textarea:focus {
  outline: none;
  border-color: var(--accent);
}

.select {
  cursor: pointer;
}

/* Loading Animation */
.loading {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--bg-tertiary);
  border-radius: 50%;
  border-top-color: var(--accent);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Fade Transition */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* Slide Transition */
.slide-enter-active, .slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}

/* Utility Classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.text-sm {
  font-size: 12px;
}

.text-xs {
  font-size: 11px;
}

.opacity-60 {
  opacity: 0.6;
}

.cursor-pointer {
  cursor: pointer;
}

/* Generated Text Styles */
.generated-text {
  color: var(--text-generated);
  background: rgba(136, 136, 136, 0.1);
  border-radius: 3px;
  padding: 0 2px;
  position: relative;
}

.generated-text:hover {
  background: rgba(136, 136, 136, 0.2);
}

/* Accept Button */
.accept-btn {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--success);
  border: none;
  color: white;
  font-size: 10px;
  cursor: pointer;
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.generated-text:hover .accept-btn {
  display: flex;
}

/* Error Message */
.error-message {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  color: var(--error);
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

/* Success Message */
.success-message {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: var(--success);
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}
