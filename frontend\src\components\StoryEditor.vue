<template>
  <div class="story-editor">
    <!-- Error Message -->
    <div v-if="error" class="error-message">
      {{ error }}
    </div>
    
    <!-- Main Editor -->
    <div class="editor-container">
      <div class="editor-wrapper">
        <textarea
          ref="editorRef"
          v-model="localValue"
          class="story-textarea"
          placeholder="Start writing your story here... The AI will continue from where you stop."
          @input="handleInput"
          @keydown="handleKeydown"
          @scroll="handleScroll"
        ></textarea>
        
        <!-- Generated Text Overlay -->
        <div 
          v-if="generatedText" 
          class="generated-overlay"
          :style="overlayStyle"
        >
          <span class="generated-text">
            {{ generatedText }}
            <button 
              class="accept-btn" 
              @click="$emit('accept-generated')"
              title="Accept (Tab)"
            >
              ✓
            </button>
          </span>
        </div>
        
        <!-- Loading Indicator -->
        <div v-if="isGenerating" class="loading-indicator">
          <div class="loading"></div>
          <span>AI is writing...</span>
        </div>
      </div>
    </div>
    
    <!-- Controls -->
    <div class="editor-controls">
      <div class="control-group">
        <button 
          class="btn" 
          @click="$emit('generate')"
          :disabled="isGenerating || !localValue.trim()"
        >
          <span v-if="isGenerating" class="loading"></span>
          {{ isGenerating ? 'Generating...' : 'Generate' }}
        </button>
        
        <button 
          v-if="generatedText"
          class="btn btn-success" 
          @click="$emit('accept-generated')"
        >
          Accept (Tab)
        </button>
        
        <button 
          v-if="generatedText"
          class="btn btn-secondary" 
          @click="$emit('reject-generated')"
        >
          Reject
        </button>
        
        <button 
          v-if="isGenerating"
          class="btn btn-secondary" 
          @click="$emit('stop-generation')"
        >
          Stop
        </button>
      </div>
      
      <div class="control-group">
        <button class="btn btn-secondary" @click="undo" :disabled="!canUndo">
          Undo
        </button>
        <button class="btn btn-secondary" @click="redo" :disabled="!canRedo">
          Redo
        </button>
        <button class="btn btn-secondary" @click="clearStory">
          Clear
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, nextTick, onMounted } from 'vue'

export default {
  name: 'StoryEditor',
  props: {
    modelValue: String,
    isGenerating: Boolean,
    generatedText: String,
    error: String
  },
  emits: ['update:modelValue', 'generate', 'accept-generated', 'reject-generated', 'stop-generation'],
  setup(props, { emit }) {
    const editorRef = ref(null)
    const localValue = ref(props.modelValue || '')
    const overlayStyle = ref({})
    
    // Undo/Redo functionality
    const history = ref([])
    const historyIndex = ref(-1)
    const maxHistorySize = 50
    
    const canUndo = computed(() => historyIndex.value > 0)
    const canRedo = computed(() => historyIndex.value < history.value.length - 1)
    
    // Watch for external changes
    watch(() => props.modelValue, (newValue) => {
      if (newValue !== localValue.value) {
        localValue.value = newValue || ''
      }
    })
    
    // Watch for generated text changes to update overlay position
    watch(() => props.generatedText, () => {
      if (props.generatedText) {
        nextTick(() => {
          updateOverlayPosition()
        })
      }
    })
    
    const handleInput = (event) => {
      const value = event.target.value
      localValue.value = value
      emit('update:modelValue', value)
      
      // Add to history
      addToHistory(value)
      
      // Auto-scroll to bottom
      nextTick(() => {
        scrollToBottom()
      })
    }
    
    const handleKeydown = (event) => {
      // Tab to accept generated text
      if (event.key === 'Tab' && props.generatedText) {
        event.preventDefault()
        emit('accept-generated')
      }
      
      // Ctrl+Enter to generate
      if (event.key === 'Enter' && event.ctrlKey) {
        event.preventDefault()
        if (!props.isGenerating && localValue.value.trim()) {
          emit('generate')
        }
      }
      
      // Ctrl+Z for undo
      if (event.key === 'z' && event.ctrlKey && !event.shiftKey) {
        event.preventDefault()
        undo()
      }
      
      // Ctrl+Y or Ctrl+Shift+Z for redo
      if ((event.key === 'y' && event.ctrlKey) || 
          (event.key === 'z' && event.ctrlKey && event.shiftKey)) {
        event.preventDefault()
        redo()
      }
    }
    
    const handleScroll = () => {
      if (props.generatedText) {
        updateOverlayPosition()
      }
    }
    
    const updateOverlayPosition = () => {
      if (!editorRef.value || !props.generatedText) return
      
      const textarea = editorRef.value
      const textLength = localValue.value.length
      
      // Create a temporary element to measure text position
      const temp = document.createElement('div')
      temp.style.cssText = getComputedStyle(textarea).cssText
      temp.style.position = 'absolute'
      temp.style.visibility = 'hidden'
      temp.style.height = 'auto'
      temp.style.width = textarea.clientWidth + 'px'
      temp.style.whiteSpace = 'pre-wrap'
      temp.style.wordWrap = 'break-word'
      
      // Add text up to cursor position
      temp.textContent = localValue.value
      document.body.appendChild(temp)
      
      const textHeight = temp.scrollHeight
      const lineHeight = parseInt(getComputedStyle(textarea).lineHeight)
      
      document.body.removeChild(temp)
      
      // Position overlay at the end of text
      overlayStyle.value = {
        top: Math.min(textHeight - textarea.scrollTop, textarea.clientHeight - lineHeight) + 'px',
        left: '12px'
      }
    }
    
    const scrollToBottom = () => {
      if (editorRef.value) {
        editorRef.value.scrollTop = editorRef.value.scrollHeight
      }
    }
    
    const addToHistory = (value) => {
      // Remove any history after current index
      history.value = history.value.slice(0, historyIndex.value + 1)
      
      // Add new state
      history.value.push(value)
      historyIndex.value = history.value.length - 1
      
      // Limit history size
      if (history.value.length > maxHistorySize) {
        history.value.shift()
        historyIndex.value--
      }
    }
    
    const undo = () => {
      if (canUndo.value) {
        historyIndex.value--
        const value = history.value[historyIndex.value]
        localValue.value = value
        emit('update:modelValue', value)
      }
    }
    
    const redo = () => {
      if (canRedo.value) {
        historyIndex.value++
        const value = history.value[historyIndex.value]
        localValue.value = value
        emit('update:modelValue', value)
      }
    }
    
    const clearStory = () => {
      if (confirm('Are you sure you want to clear the entire story?')) {
        localValue.value = ''
        emit('update:modelValue', '')
        addToHistory('')
      }
    }
    
    // Initialize history
    onMounted(() => {
      addToHistory(localValue.value)
    })
    
    return {
      editorRef,
      localValue,
      overlayStyle,
      canUndo,
      canRedo,
      handleInput,
      handleKeydown,
      handleScroll,
      undo,
      redo,
      clearStory
    }
  }
}
</script>

<style scoped>
.story-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-container {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.editor-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
}

.story-textarea {
  flex: 1;
  width: 100%;
  min-height: 400px;
  padding: 16px;
  border: 1px solid var(--border);
  border-radius: 8px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 16px;
  line-height: 1.6;
  font-family: 'Georgia', 'Times New Roman', serif;
  resize: none;
  outline: none;
  transition: border-color 0.2s ease;
}

.story-textarea:focus {
  border-color: var(--accent);
}

.story-textarea::placeholder {
  color: var(--text-secondary);
  font-style: italic;
}

.generated-overlay {
  position: absolute;
  pointer-events: none;
  z-index: 10;
}

.generated-text {
  background: rgba(136, 136, 136, 0.15);
  color: var(--text-generated);
  padding: 2px 4px;
  border-radius: 3px;
  position: relative;
  pointer-events: auto;
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 16px;
  line-height: 1.6;
}

.generated-text:hover {
  background: rgba(136, 136, 136, 0.25);
}

.accept-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--success);
  border: none;
  color: white;
  font-size: 12px;
  cursor: pointer;
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 20;
  transition: all 0.2s ease;
}

.generated-text:hover .accept-btn {
  display: flex;
}

.accept-btn:hover {
  background: #45a049;
  transform: scale(1.1);
}

.loading-indicator {
  position: absolute;
  bottom: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--bg-tertiary);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  color: var(--text-secondary);
  border: 1px solid var(--border);
}

.editor-controls {
  padding: 16px 20px;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.control-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

@media (max-width: 768px) {
  .editor-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .control-group {
    justify-content: center;
  }
  
  .story-textarea {
    font-size: 14px;
  }
}

/* Smooth transitions */
.generated-text {
  animation: fadeInSlide 0.3s ease-out;
}

@keyframes fadeInSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
