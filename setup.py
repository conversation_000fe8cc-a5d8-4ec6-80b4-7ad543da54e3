#!/usr/bin/env python3
"""
AI Story Writer - Setup and <PERSON> Script
This script handles automatic installation and running of the application.
"""

import os
import sys
import subprocess
import platform
import urllib.request
import shutil
from pathlib import Path

def print_header():
    print("=" * 50)
    print("    AI Story Writer - Setup and Run")
    print("=" * 50)
    print()

def check_python():
    """Check if Python is properly installed"""
    try:
        version = sys.version_info
        if version.major >= 3 and version.minor >= 8:
            print(f"[INFO] Python {version.major}.{version.minor}.{version.micro} found!")
            return True
        else:
            print(f"[WARNING] Python {version.major}.{version.minor} found, but 3.8+ recommended")
            return True
    except:
        return False

def check_node():
    """Check if Node.js is installed"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"[INFO] Node.js {result.stdout.strip()} found!")
            return True
    except FileNotFoundError:
        pass
    return False

def install_node():
    """Install Node.js automatically"""
    print("[INFO] Node.js not found. Installing...")
    
    system = platform.system().lower()
    if system == "windows":
        # Download and install Node.js for Windows
        url = "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi"
        installer_path = "temp/nodejs-installer.msi"
        
        os.makedirs("temp", exist_ok=True)
        
        print("[INFO] Downloading Node.js installer...")
        urllib.request.urlretrieve(url, installer_path)
        
        print("[INFO] Installing Node.js (this may take a few minutes)...")
        subprocess.run(['msiexec', '/i', installer_path, '/quiet', '/norestart'], check=True)
        
        # Update PATH
        os.environ['PATH'] = os.environ.get('PATH', '') + ';C:\\Program Files\\nodejs'
        
        return check_node()
    else:
        print("[ERROR] Automatic Node.js installation not supported on this platform")
        print("Please install Node.js manually from https://nodejs.org")
        return False

def setup_python_env():
    """Set up Python virtual environment and dependencies"""
    venv_path = Path("venv")
    
    if not venv_path.exists():
        print("[INFO] Creating Python virtual environment...")
        subprocess.run([sys.executable, '-m', 'venv', 'venv'], check=True)
    
    # Determine the correct Python executable in venv
    if platform.system() == "Windows":
        python_exe = venv_path / "Scripts" / "python.exe"
        pip_exe = venv_path / "Scripts" / "pip.exe"
    else:
        python_exe = venv_path / "bin" / "python"
        pip_exe = venv_path / "bin" / "pip"
    
    # Check if Flask is already installed
    try:
        result = subprocess.run([str(python_exe), '-c', 'import flask'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("[INFO] Installing Python dependencies...")
            subprocess.run([str(pip_exe), 'install', '-r', 'requirements.txt'], check=True)
    except:
        print("[INFO] Installing Python dependencies...")
        subprocess.run([str(pip_exe), 'install', '-r', 'requirements.txt'], check=True)
    
    return python_exe

def setup_node_env():
    """Set up Node.js dependencies"""
    frontend_path = Path("frontend")
    node_modules = frontend_path / "node_modules"
    
    if not node_modules.exists():
        print("[INFO] Installing Node.js dependencies...")
        subprocess.run(['npm', 'install'], cwd=frontend_path, check=True)

def start_application(python_exe):
    """Start the backend and frontend servers"""
    print()
    print("[INFO] Starting AI Story Writer...")
    print("[INFO] Backend will run on http://localhost:5000")
    print("[INFO] Frontend will run on http://localhost:3000")
    print("[INFO] Your browser will open automatically")
    print()
    
    # Start backend
    backend_process = subprocess.Popen([str(python_exe), 'backend/app.py'])
    
    # Wait a moment for backend to start
    import time
    time.sleep(3)
    
    # Start frontend
    frontend_process = subprocess.Popen(['npm', 'run', 'dev'], cwd='frontend')
    
    # Wait for frontend to start then open browser
    time.sleep(5)
    
    # Open browser
    import webbrowser
    webbrowser.open('http://localhost:3000')
    
    print("[SUCCESS] AI Story Writer is running!")
    print("[INFO] Press Ctrl+C to stop the application")
    print()
    
    try:
        # Keep the script running
        backend_process.wait()
    except KeyboardInterrupt:
        print("\n[INFO] Stopping services...")
        backend_process.terminate()
        frontend_process.terminate()
        
        # Wait for processes to terminate
        try:
            backend_process.wait(timeout=5)
            frontend_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            backend_process.kill()
            frontend_process.kill()
    
    # Clean up temp directory
    if os.path.exists("temp"):
        shutil.rmtree("temp", ignore_errors=True)

def main():
    print_header()
    
    # Check Python
    if not check_python():
        print("[ERROR] Python 3.8+ is required but not found")
        print("This script requires Python to run. Please install Python first.")
        input("Press Enter to exit...")
        sys.exit(1)
    
    # Check Node.js
    if not check_node():
        try:
            if not install_node():
                print("[ERROR] Please install Node.js manually from https://nodejs.org")
                input("Press Enter to exit...")
                sys.exit(1)
        except Exception as e:
            print(f"[ERROR] Failed to install Node.js: {e}")
            print("Please install Node.js manually from https://nodejs.org")
            input("Press Enter to exit...")
            sys.exit(1)
    
    try:
        # Set up Python environment
        python_exe = setup_python_env()
        
        # Set up Node.js environment
        setup_node_env()
        
        # Start the application
        start_application(python_exe)
        
    except Exception as e:
        print(f"[ERROR] Setup failed: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
