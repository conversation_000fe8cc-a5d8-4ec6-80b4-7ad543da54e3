<template>
  <div class="sidebar" :class="{ 'open': isOpen }">
    <div class="sidebar-header">
      <h2>AI Story Writer</h2>
      <button class="btn btn-secondary btn-small" @click="$emit('toggle')">
        ×
      </button>
    </div>
    
    <div class="sidebar-content">
      <!-- Tabs -->
      <div class="tabs">
        <button 
          class="tab" 
          :class="{ active: activeTab === 'text' }"
          @click="activeTab = 'text'"
        >
          Text Settings
        </button>
        <button 
          class="tab" 
          :class="{ active: activeTab === 'image' }"
          @click="activeTab = 'image'"
          disabled
        >
          Image Settings
        </button>
      </div>
      
      <!-- Text Settings Tab -->
      <div v-if="activeTab === 'text'" class="tab-content">
        <!-- Provider Selection -->
        <div class="form-group">
          <label>AI Provider</label>
          <select 
            class="select" 
            :value="settings.provider"
            @change="updateProvider($event.target.value)"
          >
            <option value="">Select Provider</option>
            <option 
              v-for="provider in config.providers" 
              :key="provider.id"
              :value="provider.id"
            >
              {{ provider.name }}
            </option>
          </select>
        </div>
        
        <!-- Model Selection -->
        <div class="form-group" v-if="selectedProvider">
          <label>Model</label>
          <select 
            class="select" 
            :value="settings.model"
            @change="updateSetting('model', $event.target.value)"
          >
            <option value="">Select Model</option>
            <option 
              v-for="model in selectedProvider.models" 
              :key="model.id"
              :value="model.id"
            >
              {{ model.name }}
            </option>
          </select>
          <div v-if="selectedModel" class="model-description">
            {{ selectedModel.description }}
          </div>
        </div>
        
        <!-- API Key -->
        <div class="form-group" v-if="selectedProvider?.api_key_required">
          <label>API Key</label>
          <input 
            type="password" 
            class="input" 
            :value="settings.apiKey"
            @input="updateSetting('apiKey', $event.target.value)"
            placeholder="Enter your API key"
          >
          <div class="api-key-help">
            <a :href="selectedProvider.api_key_url" target="_blank" class="link">
              Get API Key →
            </a>
          </div>
        </div>
        
        <!-- System Prompt -->
        <div class="form-group">
          <label>System Prompt</label>
          <textarea 
            class="textarea" 
            rows="3"
            :value="settings.systemPrompt"
            @input="updateSetting('systemPrompt', $event.target.value)"
            placeholder="Instructions for the AI..."
          ></textarea>
        </div>
        
        <!-- Temperature -->
        <div class="form-group">
          <label>Creativity (Temperature): {{ settings.temperature }}</label>
          <input 
            type="range" 
            class="range" 
            min="0" 
            max="2" 
            step="0.1"
            :value="settings.temperature"
            @input="updateSetting('temperature', parseFloat($event.target.value))"
          >
          <div class="range-labels">
            <span>Conservative</span>
            <span>Creative</span>
          </div>
        </div>
        
        <!-- Max Tokens -->
        <div class="form-group">
          <label>Max Words: {{ settings.maxTokens }}</label>
          <input 
            type="range" 
            class="range" 
            min="50" 
            max="500" 
            step="10"
            :value="settings.maxTokens"
            @input="updateSetting('maxTokens', parseInt($event.target.value))"
          >
          <div class="range-labels">
            <span>Short</span>
            <span>Long</span>
          </div>
        </div>
        
        <!-- Auto Save -->
        <div class="form-group">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              :checked="settings.autoSave"
              @change="updateSetting('autoSave', $event.target.checked)"
            >
            Auto-save story
          </label>
        </div>
      </div>
      
      <!-- Image Settings Tab (Placeholder) -->
      <div v-if="activeTab === 'image'" class="tab-content">
        <div class="coming-soon">
          <p>Image generation settings coming soon!</p>
        </div>
      </div>
    </div>
    
    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
      <div class="export-import">
        <button class="btn btn-secondary" @click="handleExport">
          Export Story
        </button>
        <button class="btn btn-secondary" @click="$emit('import')">
          Import Story
        </button>
      </div>
    </div>
    
    <!-- Export Modal -->
    <div v-if="showExportModal" class="modal-overlay" @click="showExportModal = false">
      <div class="modal" @click.stop>
        <h3>Export Story</h3>
        <p>Enter a password to encrypt your story:</p>
        <input 
          type="password" 
          class="input" 
          v-model="exportPassword"
          placeholder="Encryption password"
          @keyup.enter="confirmExport"
        >
        <div class="modal-actions">
          <button class="btn btn-secondary" @click="showExportModal = false">
            Cancel
          </button>
          <button class="btn" @click="confirmExport" :disabled="!exportPassword">
            Export
          </button>
        </div>
        <div v-if="exportError" class="error-message">
          {{ exportError }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'Sidebar',
  props: {
    isOpen: Boolean,
    config: Object,
    settings: Object
  },
  emits: ['toggle', 'update-settings', 'export', 'import'],
  setup(props, { emit }) {
    const activeTab = ref('text')
    const showExportModal = ref(false)
    const exportPassword = ref('')
    const exportError = ref('')
    
    const selectedProvider = computed(() => {
      return props.config.providers?.find(p => p.id === props.settings.provider)
    })
    
    const selectedModel = computed(() => {
      return selectedProvider.value?.models?.find(m => m.id === props.settings.model)
    })
    
    const updateSetting = (key, value) => {
      emit('update-settings', { [key]: value })
    }
    
    const updateProvider = (providerId) => {
      const provider = props.config.providers?.find(p => p.id === providerId)
      const updates = { provider: providerId }
      
      // Reset model when provider changes
      if (provider?.models?.length > 0) {
        updates.model = provider.models[0].id
      } else {
        updates.model = ''
      }
      
      emit('update-settings', updates)
    }
    
    const handleExport = () => {
      showExportModal.value = true
      exportPassword.value = ''
      exportError.value = ''
    }
    
    const confirmExport = async () => {
      if (!exportPassword.value) return
      
      try {
        const result = await emit('export', exportPassword.value)
        if (result?.success) {
          showExportModal.value = false
        } else {
          exportError.value = result?.error || 'Export failed'
        }
      } catch (err) {
        exportError.value = err.message
      }
    }
    
    return {
      activeTab,
      showExportModal,
      exportPassword,
      exportError,
      selectedProvider,
      selectedModel,
      updateSetting,
      updateProvider,
      handleExport,
      confirmExport
    }
  }
}
</script>

<style scoped>
.sidebar {
  width: 350px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  position: relative;
  z-index: 100;
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.sidebar-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--border);
}

.tab {
  flex: 1;
  padding: 10px 16px;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 14px;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab:hover:not(:disabled) {
  color: var(--text-primary);
}

.tab.active {
  color: var(--accent);
  border-bottom-color: var(--accent);
}

.tab:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.model-description {
  margin-top: 4px;
  font-size: 12px;
  color: var(--text-secondary);
  font-style: italic;
}

.api-key-help {
  margin-top: 4px;
}

.link {
  color: var(--accent);
  text-decoration: none;
  font-size: 12px;
}

.link:hover {
  text-decoration: underline;
}

.range {
  width: 100%;
  margin: 8px 0;
}

.range-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-secondary);
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.coming-soon {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid var(--border);
  flex-shrink: 0;
}

.export-import {
  display: flex;
  gap: 10px;
}

.export-import .btn {
  flex: 1;
  justify-content: center;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: var(--bg-secondary);
  padding: 24px;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  border: 1px solid var(--border);
}

.modal h3 {
  margin-bottom: 16px;
  color: var(--text-primary);
}

.modal p {
  margin-bottom: 16px;
  color: var(--text-secondary);
}

.modal-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.modal-actions .btn {
  flex: 1;
  justify-content: center;
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
}
</style>
