@echo off
title AI Story Writer - Setup and Run
echo ========================================
echo    AI Story Writer - Setup and Run
echo ========================================
echo.

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [INFO] Python not found. Installing Python automatically...
    call :install_python
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install Python automatically
        echo Please install Python 3.8+ manually from https://python.org
        pause
        exit /b 1
    )
)

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [INFO] Node.js not found. Installing Node.js automatically...
    call :install_nodejs
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install Node.js automatically
        echo Please install Node.js manually from https://nodejs.org
        pause
        exit /b 1
    )
)

echo [INFO] Python and Node.js found!
echo.

:: Create virtual environment if it doesn't exist
if not exist "venv" (
    echo [INFO] Creating Python virtual environment...
    python -m venv venv
)

:: Activate virtual environment
echo [INFO] Activating virtual environment...
call venv\Scripts\activate.bat

:: Install Python dependencies
if not exist "venv\Lib\site-packages\flask" (
    echo [INFO] Installing Python dependencies...
    pip install -r requirements.txt
)

:: Install Node.js dependencies
if not exist "frontend\node_modules" (
    echo [INFO] Installing Node.js dependencies...
    cd frontend
    npm install
    cd ..
)

:: Start the application
echo.
echo [INFO] Starting AI Story Writer...
echo [INFO] Backend will run on http://localhost:5000
echo [INFO] Frontend will run on http://localhost:3000
echo [INFO] Your browser will open automatically
echo.

:: Start backend in background
start /b cmd /c "call venv\Scripts\activate.bat && python backend\app.py"

:: Wait a moment for backend to start
timeout /t 3 /nobreak >nul

:: Start frontend
cd frontend
start /b cmd /c "npm run dev"

:: Wait for frontend to start then open browser
timeout /t 5 /nobreak >nul
start http://localhost:3000

echo.
echo [SUCCESS] AI Story Writer is running!
echo [INFO] Close this window to stop the application
echo [INFO] Press Ctrl+C to stop if needed
echo.
pause
goto :eof

:: Function to install Python
:install_python
echo [INFO] Downloading Python installer...
if not exist "temp" mkdir temp
powershell -Command "& {Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile 'temp\python-installer.exe'}"
if %errorlevel% neq 0 (
    echo [ERROR] Failed to download Python installer
    exit /b 1
)

echo [INFO] Installing Python (this may take a few minutes)...
temp\python-installer.exe /quiet InstallAllUsers=0 PrependPath=1 Include_test=0
if %errorlevel% neq 0 (
    echo [ERROR] Python installation failed
    exit /b 1
)

echo [INFO] Refreshing environment variables...
call :refresh_env

:: Verify Python installation
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python installation verification failed
    exit /b 1
)

echo [SUCCESS] Python installed successfully!
exit /b 0

:: Function to install Node.js
:install_nodejs
echo [INFO] Downloading Node.js installer...
if not exist "temp" mkdir temp
powershell -Command "& {Invoke-WebRequest -Uri 'https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi' -OutFile 'temp\nodejs-installer.msi'}"
if %errorlevel% neq 0 (
    echo [ERROR] Failed to download Node.js installer
    exit /b 1
)

echo [INFO] Installing Node.js (this may take a few minutes)...
msiexec /i temp\nodejs-installer.msi /quiet /norestart
if %errorlevel% neq 0 (
    echo [ERROR] Node.js installation failed
    exit /b 1
)

echo [INFO] Refreshing environment variables...
call :refresh_env

:: Verify Node.js installation
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js installation verification failed
    exit /b 1
)

echo [SUCCESS] Node.js installed successfully!
exit /b 0

:: Function to refresh environment variables
:refresh_env
:: Update PATH from registry
for /f "tokens=2*" %%A in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "UserPath=%%B"
for /f "tokens=2*" %%A in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SystemPath=%%B"
set "PATH=%SystemPath%;%UserPath%"
exit /b 0
