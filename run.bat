@echo off
title AI Story Writer - Simple Setup
echo ========================================
echo    AI Story Writer - Simple Setup
echo ========================================
echo.

:: Check if Python is installed
echo [1/4] Checking Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed or not in PATH
    echo.
    echo Please install Python from: https://python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    echo Then run this script again.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [OK] Python %PYTHON_VERSION%

:: Check if pip is available
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] pip is not available
    echo Please reinstall Python with pip included
    pause
    exit /b 1
)

:: Check if Node.js is installed
echo [2/4] Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo.
    echo Then run this script again.
    pause
    exit /b 1
)

for /f "tokens=1" %%i in ('node --version 2^>^&1') do set NODE_VERSION=%%i
echo [OK] Node.js %NODE_VERSION%

echo.

:: Install Python dependencies directly (no venv for simplicity)
echo [3/4] Installing Python packages...
pip install -r requirements.txt --user
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install Python dependencies
    echo Try running as administrator or check your internet connection
    pause
    exit /b 1
)

:: Install Node.js dependencies
echo [4/4] Installing Node.js packages...
if not exist "frontend\node_modules" (
    cd frontend
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install Node.js dependencies
        echo Check your internet connection
        cd ..
        pause
        exit /b 1
    )
    cd ..
) else (
    echo Node.js packages already installed
)

:: Start the application
echo.
echo Starting AI Story Writer...
echo.
echo Backend: http://localhost:5000
echo Frontend: http://localhost:3000
echo.

:: Start backend in background
echo Starting backend server...
start /b cmd /c "python backend\app.py"

:: Wait a moment for backend to start
timeout /t 3 /nobreak >nul

:: Start frontend
echo Starting frontend server...
cd frontend
start /b cmd /c "npm run dev"

:: Wait for frontend to start then open browser
echo Waiting for servers to start...
timeout /t 8 /nobreak >nul

echo Opening browser...
start http://localhost:3000

cd ..
echo.
echo ========================================
echo [SUCCESS] AI Story Writer is running!
echo ========================================
echo.
echo Your app is now available at: http://localhost:3000
echo.
echo To stop the application:
echo - Close this window, or
echo - Press any key below
echo.
pause >nul

:: Kill the background processes
echo.
echo Stopping servers...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1

echo Servers stopped. You can now close this window.
pause >nul
