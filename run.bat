@echo off
title AI Story Writer - Setup and Run
echo ========================================
echo    AI Story Writer - Setup and Run
echo ========================================
echo.

:: Check if Python is installed
echo [INFO] Checking for Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8+ from: https://python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    echo After installing Python, run this script again.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [SUCCESS] Python %PYTHON_VERSION% found!

:: Check if Node.js is installed
echo [INFO] Checking for Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo.
    echo After installing Node.js, run this script again.
    pause
    exit /b 1
)

for /f "tokens=1" %%i in ('node --version 2^>^&1') do set NODE_VERSION=%%i
echo [SUCCESS] Node.js %NODE_VERSION% found!

echo.

:: Create virtual environment if it doesn't exist
if not exist "venv" (
    echo [INFO] Creating Python virtual environment...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to create virtual environment
        pause
        exit /b 1
    )
)

:: Activate virtual environment
echo [INFO] Activating virtual environment...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo [ERROR] Failed to activate virtual environment
    pause
    exit /b 1
)

:: Install Python dependencies
if not exist "venv\Lib\site-packages\flask" (
    echo [INFO] Installing Python dependencies...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install Python dependencies
        pause
        exit /b 1
    )
)

:: Install Node.js dependencies
if not exist "frontend\node_modules" (
    echo [INFO] Installing Node.js dependencies...
    cd frontend
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install Node.js dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
)

:: Start the application
echo.
echo [INFO] Starting AI Story Writer...
echo [INFO] Backend will run on http://localhost:5000
echo [INFO] Frontend will run on http://localhost:3000
echo [INFO] Your browser will open automatically
echo.

:: Start backend in background
echo [INFO] Starting backend server...
start /b cmd /c "call venv\Scripts\activate.bat && python backend\app.py"

:: Wait a moment for backend to start
echo [INFO] Waiting for backend to start...
timeout /t 3 /nobreak >nul

:: Start frontend
echo [INFO] Starting frontend server...
cd frontend
start /b cmd /c "npm run dev"

:: Wait for frontend to start then open browser
echo [INFO] Waiting for frontend to start...
timeout /t 8 /nobreak >nul

echo [INFO] Opening browser...
start http://localhost:3000

cd ..
echo.
echo [SUCCESS] AI Story Writer is running!
echo [INFO] Backend: http://localhost:5000
echo [INFO] Frontend: http://localhost:3000
echo [INFO] Close this window to stop the application
echo.
echo Press any key to stop the servers...
pause >nul

:: Kill the background processes
echo [INFO] Stopping servers...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1
echo [INFO] Servers stopped.
