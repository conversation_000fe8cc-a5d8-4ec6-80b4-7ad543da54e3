@echo off
title AI Story Writer - Setup and Run
echo ========================================
echo    AI Story Writer - Setup and Run
echo ========================================
echo.

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org
    pause
    exit /b 1
)

echo [INFO] Python and Node.js found!
echo.

:: Create virtual environment if it doesn't exist
if not exist "venv" (
    echo [INFO] Creating Python virtual environment...
    python -m venv venv
)

:: Activate virtual environment
echo [INFO] Activating virtual environment...
call venv\Scripts\activate.bat

:: Install Python dependencies
if not exist "venv\Lib\site-packages\flask" (
    echo [INFO] Installing Python dependencies...
    pip install -r requirements.txt
)

:: Install Node.js dependencies
if not exist "frontend\node_modules" (
    echo [INFO] Installing Node.js dependencies...
    cd frontend
    npm install
    cd ..
)

:: Start the application
echo.
echo [INFO] Starting AI Story Writer...
echo [INFO] Backend will run on http://localhost:5000
echo [INFO] Frontend will run on http://localhost:3000
echo [INFO] Your browser will open automatically
echo.

:: Start backend in background
start /b cmd /c "call venv\Scripts\activate.bat && python backend\app.py"

:: Wait a moment for backend to start
timeout /t 3 /nobreak >nul

:: Start frontend
cd frontend
start /b cmd /c "npm run dev"

:: Wait for frontend to start then open browser
timeout /t 5 /nobreak >nul
start http://localhost:3000

echo.
echo [SUCCESS] AI Story Writer is running!
echo [INFO] Close this window to stop the application
echo [INFO] Press Ctrl+C to stop if needed
echo.
pause
