/**
 * Local Storage composable for persisting user data
 */
export function useStorage() {
  const saveToStorage = (key, value) => {
    try {
      const serialized = JSON.stringify(value)
      localStorage.setItem(`ai-story-writer-${key}`, serialized)
    } catch (error) {
      console.error('Failed to save to localStorage:', error)
    }
  }
  
  const loadFromStorage = (key) => {
    try {
      const item = localStorage.getItem(`ai-story-writer-${key}`)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error('Failed to load from localStorage:', error)
      return null
    }
  }
  
  const removeFromStorage = (key) => {
    try {
      localStorage.removeItem(`ai-story-writer-${key}`)
    } catch (error) {
      console.error('Failed to remove from localStorage:', error)
    }
  }
  
  const clearStorage = () => {
    try {
      const keys = Object.keys(localStorage).filter(key => 
        key.startsWith('ai-story-writer-')
      )
      keys.forEach(key => localStorage.removeItem(key))
    } catch (error) {
      console.error('Failed to clear localStorage:', error)
    }
  }
  
  return {
    saveToStorage,
    loadFromStorage,
    removeFromStorage,
    clearStorage
  }
}
