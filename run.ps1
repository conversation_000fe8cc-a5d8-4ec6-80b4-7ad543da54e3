# AI Story Writer - PowerShell Setup and Run Script
# This script automatically installs dependencies and runs the application

param(
    [switch]$SkipInstall = $false
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    AI Story Writer - Setup and Run" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to install Python
function Install-Python {
    Write-Host "[INFO] Installing Python..." -ForegroundColor Yellow
    
    # Create temp directory
    if (!(Test-Path "temp")) {
        New-Item -ItemType Directory -Path "temp" | Out-Null
    }
    
    # Download Python installer
    $pythonUrl = "https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe"
    $pythonInstaller = "temp\python-installer.exe"
    
    try {
        Write-Host "[INFO] Downloading Python installer..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $pythonUrl -OutFile $pythonInstaller -UseBasicParsing
        
        Write-Host "[INFO] Installing Python (this may take a few minutes)..." -ForegroundColor Yellow
        Start-Process -FilePath $pythonInstaller -ArgumentList "/quiet", "InstallAllUsers=0", "PrependPath=1", "Include_test=0" -Wait
        
        # Refresh environment variables
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        # Verify installation
        $pythonCheck = Get-Command python -ErrorAction SilentlyContinue
        if ($pythonCheck) {
            Write-Host "[SUCCESS] Python installed successfully!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "[ERROR] Python installation verification failed" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "[ERROR] Failed to install Python: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to install Node.js
function Install-NodeJS {
    Write-Host "[INFO] Installing Node.js..." -ForegroundColor Yellow
    
    # Create temp directory
    if (!(Test-Path "temp")) {
        New-Item -ItemType Directory -Path "temp" | Out-Null
    }
    
    # Download Node.js installer
    $nodeUrl = "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi"
    $nodeInstaller = "temp\nodejs-installer.msi"
    
    try {
        Write-Host "[INFO] Downloading Node.js installer..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $nodeUrl -OutFile $nodeInstaller -UseBasicParsing
        
        Write-Host "[INFO] Installing Node.js (this may take a few minutes)..." -ForegroundColor Yellow
        Start-Process -FilePath "msiexec.exe" -ArgumentList "/i", $nodeInstaller, "/quiet", "/norestart" -Wait
        
        # Refresh environment variables
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        # Verify installation
        $nodeCheck = Get-Command node -ErrorAction SilentlyContinue
        if ($nodeCheck) {
            Write-Host "[SUCCESS] Node.js installed successfully!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "[ERROR] Node.js installation verification failed" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "[ERROR] Failed to install Node.js: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Check and install Python
$pythonCheck = Get-Command python -ErrorAction SilentlyContinue
if (!$pythonCheck -and !$SkipInstall) {
    if (!(Install-Python)) {
        Write-Host "[ERROR] Please install Python manually from https://python.org" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} elseif ($pythonCheck) {
    Write-Host "[INFO] Python found!" -ForegroundColor Green
}

# Check and install Node.js
$nodeCheck = Get-Command node -ErrorAction SilentlyContinue
if (!$nodeCheck -and !$SkipInstall) {
    if (!(Install-NodeJS)) {
        Write-Host "[ERROR] Please install Node.js manually from https://nodejs.org" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} elseif ($nodeCheck) {
    Write-Host "[INFO] Node.js found!" -ForegroundColor Green
}

Write-Host ""

# Create virtual environment if it doesn't exist
if (!(Test-Path "venv")) {
    Write-Host "[INFO] Creating Python virtual environment..." -ForegroundColor Yellow
    python -m venv venv
}

# Activate virtual environment and install Python dependencies
Write-Host "[INFO] Activating virtual environment..." -ForegroundColor Yellow
& "venv\Scripts\Activate.ps1"

if (!(Test-Path "venv\Lib\site-packages\flask")) {
    Write-Host "[INFO] Installing Python dependencies..." -ForegroundColor Yellow
    pip install -r requirements.txt
}

# Install Node.js dependencies
if (!(Test-Path "frontend\node_modules")) {
    Write-Host "[INFO] Installing Node.js dependencies..." -ForegroundColor Yellow
    Set-Location frontend
    npm install
    Set-Location ..
}

# Start the application
Write-Host ""
Write-Host "[INFO] Starting AI Story Writer..." -ForegroundColor Yellow
Write-Host "[INFO] Backend will run on http://localhost:5000" -ForegroundColor Cyan
Write-Host "[INFO] Frontend will run on http://localhost:3000" -ForegroundColor Cyan
Write-Host "[INFO] Your browser will open automatically" -ForegroundColor Cyan
Write-Host ""

# Start backend in background
$backendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    & "venv\Scripts\Activate.ps1"
    python backend\app.py
}

# Wait a moment for backend to start
Start-Sleep -Seconds 3

# Start frontend in background
$frontendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD\frontend
    npm run dev
}

# Wait for frontend to start then open browser
Start-Sleep -Seconds 5
Start-Process "http://localhost:3000"

Write-Host ""
Write-Host "[SUCCESS] AI Story Writer is running!" -ForegroundColor Green
Write-Host "[INFO] Close this window to stop the application" -ForegroundColor Yellow
Write-Host "[INFO] Press Ctrl+C to stop if needed" -ForegroundColor Yellow
Write-Host ""

# Keep the script running and monitor jobs
try {
    while ($true) {
        if ($backendJob.State -eq "Failed") {
            Write-Host "[ERROR] Backend crashed!" -ForegroundColor Red
            break
        }
        if ($frontendJob.State -eq "Failed") {
            Write-Host "[ERROR] Frontend crashed!" -ForegroundColor Red
            break
        }
        Start-Sleep -Seconds 2
    }
}
finally {
    # Cleanup jobs when script exits
    Write-Host "[INFO] Stopping services..." -ForegroundColor Yellow
    Stop-Job $backendJob -ErrorAction SilentlyContinue
    Stop-Job $frontendJob -ErrorAction SilentlyContinue
    Remove-Job $backendJob -ErrorAction SilentlyContinue
    Remove-Job $frontendJob -ErrorAction SilentlyContinue
}

# Clean up temp directory
if (Test-Path "temp") {
    Remove-Item "temp" -Recurse -Force -ErrorAction SilentlyContinue
}
