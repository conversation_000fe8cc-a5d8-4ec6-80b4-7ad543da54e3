@echo off
title AI Story Writer - Dependency Installer
echo ========================================
echo   AI Story Writer - Dependency Installer
echo ========================================
echo.
echo This script will download and install Python and Node.js
echo You will need to run the installers manually.
echo.
pause

:: Create temp directory
if not exist "temp" mkdir temp

:: Check if Python is already installed
python --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo [INFO] Python %PYTHON_VERSION% is already installed!
) else (
    echo [INFO] Downloading Python installer...
    powershell -Command "& {Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile 'temp\python-installer.exe'}"
    if %errorlevel% equ 0 (
        echo [SUCCESS] Python installer downloaded to temp\python-installer.exe
        echo [INFO] Please run the installer and make sure to check "Add Python to PATH"
        start temp\python-installer.exe
        echo [INFO] Waiting for Python installation to complete...
        pause
    ) else (
        echo [ERROR] Failed to download Python installer
        echo Please download manually from: https://python.org/downloads/
    )
)

echo.

:: Check if Node.js is already installed
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=1" %%i in ('node --version 2^>^&1') do set NODE_VERSION=%%i
    echo [INFO] Node.js %NODE_VERSION% is already installed!
) else (
    echo [INFO] Downloading Node.js installer...
    powershell -Command "& {Invoke-WebRequest -Uri 'https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi' -OutFile 'temp\nodejs-installer.msi'}"
    if %errorlevel% equ 0 (
        echo [SUCCESS] Node.js installer downloaded to temp\nodejs-installer.msi
        echo [INFO] Please run the installer
        start temp\nodejs-installer.msi
        echo [INFO] Waiting for Node.js installation to complete...
        pause
    ) else (
        echo [ERROR] Failed to download Node.js installer
        echo Please download manually from: https://nodejs.org/
    )
)

echo.
echo [INFO] After both installations are complete, run run.bat to start the application
echo.
pause

:: Clean up
if exist "temp" rmdir /s /q temp
