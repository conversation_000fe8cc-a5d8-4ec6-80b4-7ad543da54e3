<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal" @click.stop>
      <div class="modal-header">
        <h3>Import Story</h3>
        <button class="close-btn" @click="$emit('close')">×</button>
      </div>
      
      <div class="modal-content">
        <div class="step" v-if="step === 1">
          <p>Select your encrypted story file (.aistory):</p>
          <div class="file-input-wrapper">
            <input 
              type="file" 
              ref="fileInput"
              accept=".aistory"
              @change="handleFileSelect"
              class="file-input"
            >
            <button class="btn btn-secondary" @click="$refs.fileInput.click()">
              Choose File
            </button>
            <span v-if="selectedFile" class="file-name">
              {{ selectedFile.name }}
            </span>
          </div>
        </div>
        
        <div class="step" v-if="step === 2">
          <p>Enter the password used to encrypt this story:</p>
          <input 
            type="password" 
            class="input" 
            v-model="password"
            placeholder="Decryption password"
            @keyup.enter="handleImport"
            ref="passwordInput"
          >
        </div>
        
        <div v-if="error" class="error-message">
          {{ error }}
        </div>
        
        <div v-if="success" class="success-message">
          Story imported successfully!
        </div>
      </div>
      
      <div class="modal-actions">
        <button class="btn btn-secondary" @click="$emit('close')">
          Cancel
        </button>
        <button 
          v-if="step === 1"
          class="btn" 
          @click="nextStep"
          :disabled="!selectedFile"
        >
          Next
        </button>
        <button 
          v-if="step === 2"
          class="btn" 
          @click="handleImport"
          :disabled="!password || importing"
        >
          <span v-if="importing" class="loading"></span>
          {{ importing ? 'Importing...' : 'Import' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, nextTick } from 'vue'

export default {
  name: 'ImportModal',
  emits: ['close', 'import'],
  setup(props, { emit }) {
    const step = ref(1)
    const selectedFile = ref(null)
    const password = ref('')
    const error = ref('')
    const success = ref(false)
    const importing = ref(false)
    const fileInput = ref(null)
    const passwordInput = ref(null)
    
    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        if (file.name.endsWith('.aistory')) {
          selectedFile.value = file
          error.value = ''
        } else {
          error.value = 'Please select a valid .aistory file'
          selectedFile.value = null
        }
      }
    }
    
    const nextStep = () => {
      if (selectedFile.value) {
        step.value = 2
        nextTick(() => {
          passwordInput.value?.focus()
        })
      }
    }
    
    const handleImport = async () => {
      if (!selectedFile.value || !password.value) return
      
      importing.value = true
      error.value = ''
      
      try {
        const result = await emit('import', selectedFile.value, password.value)
        
        if (result?.success) {
          success.value = true
          setTimeout(() => {
            emit('close')
          }, 1500)
        } else {
          error.value = result?.error || 'Import failed'
        }
      } catch (err) {
        error.value = err.message || 'Import failed'
      } finally {
        importing.value = false
      }
    }
    
    return {
      step,
      selectedFile,
      password,
      error,
      success,
      importing,
      fileInput,
      passwordInput,
      handleFileSelect,
      nextStep,
      handleImport
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal {
  background: var(--bg-secondary);
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  border: 1px solid var(--border);
  animation: slideIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to { 
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 20px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.modal-content {
  padding: 24px;
}

.step {
  margin-bottom: 20px;
}

.step p {
  margin-bottom: 16px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.file-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.file-input {
  display: none;
}

.file-name {
  color: var(--text-primary);
  font-size: 14px;
  padding: 8px 12px;
  background: var(--bg-tertiary);
  border-radius: 4px;
  border: 1px solid var(--border);
}

.modal-actions {
  padding: 0 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.modal-actions .btn {
  min-width: 80px;
  justify-content: center;
}

/* Step transition */
.step {
  animation: stepIn 0.3s ease;
}

@keyframes stepIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@media (max-width: 768px) {
  .modal {
    width: 95%;
    margin: 20px;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .modal-actions .btn {
    width: 100%;
  }
  
  .file-input-wrapper {
    flex-direction: column;
    align-items: stretch;
  }
  
  .file-name {
    text-align: center;
  }
}
</style>
