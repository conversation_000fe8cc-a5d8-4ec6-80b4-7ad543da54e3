/**
 * API composable for communicating with the backend
 */
export function useApi() {
  const baseUrl = '/api'
  
  const makeRequest = async (url, options = {}) => {
    try {
      const response = await fetch(`${baseUrl}${url}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }
  
  const loadConfig = async () => {
    try {
      return await makeRequest('/config')
    } catch (error) {
      console.error('Failed to load config:', error)
      return { providers: [] }
    }
  }
  
  const generateText = async (params) => {
    const response = await makeRequest('/generate', {
      method: 'POST',
      body: JSON.stringify(params)
    })
    return response.text
  }
  
  const generateTextStream = async (params, onChunk) => {
    try {
      const response = await fetch(`${baseUrl}/generate-stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''
      
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break
        
        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() // Keep incomplete line in buffer
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.error) {
                throw new Error(data.error)
              }
              if (data.chunk) {
                onChunk(data.chunk)
              }
              if (data.done) {
                return
              }
            } catch (parseError) {
              console.error('Failed to parse SSE data:', parseError)
            }
          }
        }
      }
    } catch (error) {
      console.error('Streaming request failed:', error)
      throw error
    }
  }
  
  const encryptData = async (password, data) => {
    const response = await makeRequest('/encrypt', {
      method: 'POST',
      body: JSON.stringify({ password, data })
    })
    return response.encrypted
  }
  
  const decryptData = async (password, encrypted) => {
    const response = await makeRequest('/decrypt', {
      method: 'POST',
      body: JSON.stringify({ password, encrypted })
    })
    return response.data
  }
  
  return {
    loadConfig,
    generateText,
    generateTextStream,
    encryptData,
    decryptData
  }
}
